import type { Metadata } from 'next';
import { PageContainer } from '@/components/custom/page-container';
import { Separator } from '@/components/ui/separator';
import KanbanFlowboard from '@/features/dashboard/flowboard/board';
import FlowboardHeader from '@/features/dashboard/flowboard/header';
export const metadata: Metadata = {
  title: 'Flowboard',
  description: 'Flowboard for author',
};

export default function FlowboardPage() {
  return (
    <div>
      <FlowboardHeader />
      <Separator />
      <PageContainer>
        <div className="flex flex-col gap-5 px-4 py-10">
          <KanbanFlowboard />
        </div>
      </PageContainer>
    </div>
  );
}
