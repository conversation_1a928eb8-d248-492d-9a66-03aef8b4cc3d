export const siteConfig = {
  name: "Better Flow",
  url: process.env.SITE_URL || "https://better-flow-v2.vercel.app",
  ogImage: "https://better-flow-v2.vercel.app/opengraph-image.png",
  description:
    "Better Flow connects every step of the process from drafting, reviewing, and publishing in one place for your teams to deliver stories on time.",
  links: {
    twitter: "https://twitter.com/info",
    github: "https://github.com/info",
  },
};

export const members = [
  {
    name: "<PERSON>",
    username: "liam_brown",
    role: "Founder - CEO",
    coverImage: "https://alt.tailus.io/images/team/member-one.webp",
    avatar: "/profile.svg",
    link: "#",
    _creationTime: 1_696_767_600_000,
  },
  {
    name: "<PERSON>",
    username: "elijah_jones",
    role: "Co-Founder - CT<PERSON>",
    coverImage: "https://alt.tailus.io/images/team/member-two.webp",
    avatar: "/profile.svg",
    link: "#",
    _creationTime: 1_696_767_600_000,
  },
  {
    name: "<PERSON>",
    username: "is<PERSON><PERSON>_g<PERSON><PERSON>",
    role: "Sales Manager",
    coverImage: "https://alt.tailus.io/images/team/member-three.webp",
    avatar: "/profile.svg",
    link: "#",
    _creationTime: 1_696_767_600_000,
  },
  {
    name: "Henry Lee",
    username: "henry_lee",
    role: "UX Engeneer",
    coverImage: "https://alt.tailus.io/images/team/member-four.webp",
    avatar: "/profile.svg",
    link: "#",
    _creationTime: 1_696_767_600_000,
  },
  {
    name: "Ava Williams",
    username: "ava_williams",
    role: "Interaction Designer",
    coverImage: "https://alt.tailus.io/images/team/member-five.webp",
    avatar: "/profile.svg",
    link: "#",
    _creationTime: 1_696_767_600_000,
  },
  {
    name: "Olivia Miller",
    username: "olivia_miller",
    role: "Visual Designer",
    coverImage: "https://alt.tailus.io/images/team/member-six.webp",
    avatar: "/profile.svg",
    link: "#",
    _creationTime: 1_696_767_600_000,
  },
];
