import { getAuthUserId } from "@convex-dev/auth/server";
import { v } from "convex/values";
import { asyncMap } from "convex-helpers";
import { api, internal } from "./_generated/api";
import {
  internalMutation,
  type MutationCtx,
  mutation,
  type QueryCtx,
  query,
} from "./_generated/server";
import { username } from "./utils/validators";
import { userRoleEnum } from "./schema";

export async function requireUser(ctx: QueryCtx | MutationCtx) {
  const userId = await getAuthUserId(ctx);
  if (!userId) {
    throw new Error("Unauthorized");
  }
  return userId;
}
export const currentUser = query({
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (userId === null) {
      return null;
    }
    return await ctx.db.get(userId);
  },
});
export const addUserEmail = mutation({
  args: {
    email: v.string(),
    isVerified: v.boolean(),
  },
  handler: async (ctx, { email, isVerified }) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return;
    }
    const user = await ctx.db.get(userId);
    if (!user) {
      return;
    }

    const otherEmails = user.otherEmails ?? [];

    // Check if email already exists in otherEmails
    const existingIndex = otherEmails.findIndex((e) => e.email === email);

    if (existingIndex >= 0) {
      // Update existing entry
      otherEmails[existingIndex] = {
        ...otherEmails[existingIndex],
        isVerified: isVerified,
        isPrimary: user.email === email,
      };
    } else {
      // Add new entry
      otherEmails.push({
        email,
        isVerified: isVerified,
        isPrimary: user.email === email,
      });
    }

    await ctx.db.patch(user._id, { otherEmails });
  },
});
export const removeUserEmail = mutation({
  args: {
    email: v.string(),
  },
  handler: async (ctx, { email }) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return;
    }
    const user = await ctx.db.get(userId);
    if (!user) {
      return;
    }
    const otherEmails = user.otherEmails ?? [];
    const filteredEmails = otherEmails.filter((e) => e.email !== email);
    await ctx.db.patch(user._id, { otherEmails: filteredEmails });
  },
});
export const deleteCurrentUserAccount = mutation({
  args: {},
  handler: async (ctx) => {
    const userId = await requireUser(ctx); // or getAuthUserId(ctx)

    // Get user's identity (from Convex Auth)
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    await ctx.runMutation(internal.users.deleteUserAccount, {
      userId,
    });

    return { success: true };
  },
});
export const deleteUserAccount = internalMutation({
  args: {
    userId: v.id("users"),
  },
  handler: async (ctx, args) => {
    await asyncMap(
      [
        "google",
        "github",
        "password",
        // "password-custom" /* add other providers as needed */,
      ],
      async (provider) => {
        const authAccount = await ctx.db
          .query("authAccounts")
          .withIndex("userIdAndProvider", (q) =>
            q.eq("userId", args.userId).eq("provider", provider)
          )
          .unique();
        if (!authAccount) {
          return;
        }
        await ctx.db.delete(authAccount._id);
      }
    );
    await ctx.db.delete(args.userId);
  },
});
export const getUser = query({
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return;
    }
    const user = await ctx.db.get(userId);
    if (!user) {
      return;
    }

    return {
      ...user,
      avatarUrl: user.imageId ? await ctx.storage.getUrl(user.imageId) : null,
    };
  },
});
export const updateUser = mutation({
  args: {
    username: v.string(),
    role: userRoleEnum,
  },
  handler: async (ctx, args) => {
    const userId = await requireUser(ctx);
    const user = await ctx.db.get(userId);
    if (!user) {
      return { success: false, error: "User not found" };
    }
    // check if the provider used to sign in is google the add his email to other email
    await asyncMap(
      [
        "google",
        "github",
        "password",
        // "password-custom" /* add other providers as needed */,
      ],
      async (provider) => {
        //  if provider is google add email to other emails
        if (provider === "google") {
          await ctx.runMutation(api.users.addUserEmail, {
            email: user.email as string,
            isVerified: true,
          });
        }
      }
    );
    // if id admin make him verified

    await ctx.db.patch(userId, {
      username: args.username,
      role: args.role,
      verified: args.role === "admin" ? true : false,
    });
    return { success: true };
  },
});
export const updateUsername = mutation({
  args: {
    username: v.string(),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return { success: false, error: "Unauthorized" };
    }
    const validatedUsername = username.safeParse(args.username);

    if (!validatedUsername.success) {
      throw new Error(validatedUsername.error.message);
    }
    await ctx.db.patch(userId, { username: validatedUsername.data });
    return { success: true };
  },
});
export const updateDisplayName = mutation({
  args: {
    name: v.string(),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return { success: false, error: "Unauthorized" };
    }
    await ctx.db.patch(userId, { name: args.name });
    return { success: true };
  },
});
export const updateBio = mutation({
  args: {
    bio: v.string(),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return { success: false, error: "Unauthorized" };
    }
    await ctx.db.patch(userId, { bio: args.bio });
    return { success: true };
  },
});

export const updateCoverImage = mutation({
  args: {
    coverImage: v.string(),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return { success: false, error: "Unauthorized" };
    }
    await ctx.db.patch(userId, { coverImage: args.coverImage });
    return { success: true };
  },
});
export const updatePhone = mutation({
  args: {
    phone: v.string(),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return { success: false, error: "Unauthorized" };
    }
    await ctx.db.patch(userId, { phone: args.phone });
    return { success: true };
  },
});
export const removePhone = mutation({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return { success: false, error: "Unauthorized" };
    }
    await ctx.db.patch(userId, { phone: undefined });
    return { success: true };
  },
});
export const generateUploadUrl = mutation({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("User not found");
    }
    return await ctx.storage.generateUploadUrl();
  },
});
export const updateUserImage = mutation({
  args: {
    imageId: v.id("_storage"),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return;
    }
    // first get previous image of user and delete it from storage
    const user = await ctx.db.get(userId);
    if (!user) {
      return;
    }
    if (user.imageId) {
      await ctx.storage.delete(user.imageId);
    }
    ctx.db.patch(userId, { imageId: args.imageId });
  },
});
export const removeUserImage = mutation({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return;
    }
    const user = await ctx.db.get(userId);
    if (!user) {
      return;
    }
    if (user.imageId) {
      await ctx.storage.delete(user.imageId);
    }
    ctx.db.patch(userId, { imageId: undefined, image: undefined });
  },
});
export const allUsersExceptCurrent = query({
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) return [];

    const allUsers = await ctx.db.query("users").collect();
    const users = allUsers.filter((user) => user._id !== userId);

    const usersWithAvatars = await Promise.all(
      users.map(async (user) => {
        return {
          ...user,
          avatarUrl: user.imageId
            ? await ctx.storage.getUrl(user.imageId)
            : user.image,
        };
      })
    );

    return usersWithAvatars;
  },
});

export const getAllUsers = query({
  handler: async (ctx) => {
    const allUsers = await ctx.db.query("users").collect();
    const usersWithAvatars = await Promise.all(
      allUsers.map(async (user) => {
        return {
          ...user,
          avatarUrl: user.imageId
            ? await ctx.storage.getUrl(user.imageId)
            : user.image,
        };
      })
    );
    if (!usersWithAvatars) return [];
    // remove _id, phoneVerificationTime, emailVerificationTime, isAnonymous, verified, imageId
    const usersWithoutPrivateInfo = usersWithAvatars.map((user) => {
      const {
        _id,
        phoneVerificationTime,
        emailVerificationTime,
        isAnonymous,
        verified,
        imageId,
        otherEmails,
        ...rest
      } = user;
      return rest;
    });

    return usersWithoutPrivateInfo;
  },
});
