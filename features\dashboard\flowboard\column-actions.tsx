import { EllipsisIcon, PenToolIcon } from 'lucide-react';
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import type { Id } from '@/convex/_generated/dataModel';
import CreateOrUpdateColumn from './create-edit-column';
import CreateOrUpdateTask from './create-edit-task';

export default function TaskColumnActions({
  columnId,
  title,
}: {
  columnId: Id<'taskColumns'>;
  title: string;
}) {
  const [openEditDialog, setOpenEditDialog] = useState(false);
  return (
    <>
      <Popover>
        <PopoverTrigger asChild>
          <Button size="icon" variant="ghost">
            <EllipsisIcon />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="absolute right-4 w-56 p-2">
          <div className="flex flex-col">
            <Button
              className="w-full justify-between font-normal text-muted-foreground hover:text-primary"
              onClick={() => setOpenEditDialog(true)}
              variant="ghost"
            >
              Edit
              <PenToolIcon className="size-4 text-muted-foreground" />
            </Button>
            <CreateOrUpdateTask columnId={columnId} isPopover />
          </div>
        </PopoverContent>
      </Popover>
      <CreateOrUpdateColumn
        id={columnId}
        openDialog={openEditDialog}
        setOpenDialog={setOpenEditDialog}
        title={title}
      />
    </>
  );
}
