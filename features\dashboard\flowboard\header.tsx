import { convexAuthNextjsToken } from '@convex-dev/auth/nextjs/server';
import { fetchQuery } from 'convex/nextjs';
import { redirect } from 'next/navigation';
import { PageContainer } from '@/components/custom/page-container';
import { api } from '@/convex/_generated/api';
import CreateOrUpdateColumn from './create-edit-column';
export default async function FlowboardHeader() {
  const user = await fetchQuery(
    api.users.getUser,
    {},
    { token: await convexAuthNextjsToken() }
  );

  //  check if user is admin or author
  if (!user) {
    return redirect('/sign-in');
  }
  const isAdmin = user.role === 'admin';
  return (
    <PageContainer>
      <div className="my-10 flex items-center justify-between px-2 md:px-4">
        <h1 className="font-medium text-3xl">Flowboard</h1>
        {isAdmin && (
          <div className="flex gap-2">
            <CreateOrUpdateColumn />
          </div>
        )}
      </div>
    </PageContainer>
  );
}
