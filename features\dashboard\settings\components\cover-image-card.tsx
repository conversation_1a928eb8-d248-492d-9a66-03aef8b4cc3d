'use client';
import { zod<PERSON><PERSON>olver } from '@hookform/resolvers/zod';
import { type Preloaded, useMutation, usePreloadedQuery } from 'convex/react';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { But<PERSON> } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { api } from '@/convex/_generated/api';
import { coverImageFormSchema, type TCoverImageFormValues } from '../schema';
export function CoverImageCard({
  preloadedUser,
}: {
  preloadedUser: Preloaded<typeof api.users.getUser>;
}) {
  const user = usePreloadedQuery(preloadedUser);
  const updateCoverImage = useMutation(api.users.updateCoverImage);
  const form = useForm<TCoverImageFormValues>({
    resolver: zod<PERSON><PERSON>olver(coverImageFormSchema),
    defaultValues: { coverImage: user?.coverImage || '' },
  });

  useEffect(() => {
    if (user?.coverImage) {
      form.reset({ coverImage: user.coverImage });
    }
  }, [user?.coverImage, form]);

  if (!user) {
    return null;
  }
  const handleSubmit = (values: TCoverImageFormValues) => {
    try {
      updateCoverImage({ coverImage: values.coverImage });
      toast.success('Cover image updated successfully!');
    } catch {
      toast.error('Failed to update cover image.');
    }
  };

  return (
    <Form {...form}>
      <form
        className="flex w-full flex-col items-start rounded-sm ring ring-ring/20 dark:ring-ring/40"
        onSubmit={form.handleSubmit(handleSubmit)}
      >
        <div className="flex w-full flex-col gap-4 rounded-t-sm bg-background p-6 pb-9">
          <div className="flex flex-col gap-2">
            <h2 className="font-medium text-primary text-xl">Cover Image</h2>
            <p className="font-normal text-primary/60 text-sm">
              This is your cover image. It will be displayed on your profile.
            </p>
          </div>
          <FormField
            control={form.control}
            name="coverImage"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="sr-only">Cover Image</FormLabel>
                <FormControl>
                  <Input
                    className="w-fit lg:min-w-sm dark:bg-black"
                    placeholder="Cover image"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        <div className="flex w-full flex-col justify-between gap-3 rounded-lg rounded-t-none border-border border-t bg-secondary px-6 py-3 md:flex-row md:items-center dark:bg-card">
          <p className="font-normal text-primary/60 text-sm">
            Please enter a valid image URL.
            <span className="hidden md:inline">
              use image with aspect-ratio 2 / 3
            </span>
          </p>
          <Button size="sm" type="submit">
            Save
          </Button>
        </div>
      </form>
    </Form>
  );
}
