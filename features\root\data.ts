import { members } from "@/config/web";
import type { TArticle } from "./types";

export async function getArticles(): Promise<TArticle[]> {
  const res = await fetch(`${process.env.CONVEX_SITE_URL}/articles`, {
    headers: {
      Authorization: `Bearer ${process.env.ARTICLES_API_KEY}`,
    },
  });
  const articles = await res.json();
  if (!articles) return [];
  return articles;
}

export async function getArticle(slug: string): Promise<TArticle | null> {
  // use filter
  const articles = await getArticles();
  const article = articles.find((art) => art.slug === slug);
  if (!article) return null;
  return article;
}

export function getMembers() {
  return members;
}

export function getMember(username: string) {
  const teamMembers = getMembers();
  const teamMember = teamMembers.find((member) => member.username === username);
  if (!teamMember) return null;
  return teamMember;
}

// export const articles = [
//   {
//     title: "Article 1",
//     slug: "article-1",
//     description: "This is the first article.",
//     content: "This is the content of the first article.",
//     words: 100,
//     coverImage: "/noCover_black.jpg",
//     status: "draft",
//     group: "sport",
//     userId: 1,
//     publishedAt: Date.now(),
//   },
//   {
//     title: "Article 2",
//     slug: "article-2",
//     description: "This is the second article.",
//     content: "This is the content of the second article.",
//     words: 200,
//     coverImage: "/noCover2_black.jpg",
//     status: "draft",
//     group: "culture",
//     userId: 1,
//     publishedAt: Date.now(),
//   },
// ];
